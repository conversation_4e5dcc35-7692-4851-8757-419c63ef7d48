/* 产品页面样式 */

/* 面包屑导航 */
.breadcrumb {
    background: white;
    padding: 15px 0;
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.breadcrumb-list {
    list-style: none;
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0;
    font-size: 14px;
}

.breadcrumb-list li {
    display: flex;
    align-items: center;
}

.breadcrumb-list a {
    color: #4a5568;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-list a:hover {
    color: #e53e3e;
}

.breadcrumb-list .current {
    color: #e53e3e;
    font-weight: 500;
}

.breadcrumb-separator {
    margin: 0 10px;
    color: #a0aec0;
}

/* 产品列表页面布局 */
.products-page {
    display: flex;
    gap: 20px;
    margin: 20px 0;
}

.products-sidebar {
    width: 250px;
    flex-shrink: 0;
}

.products-main {
    flex: 1;
}

/* 筛选菜单 */
.filter-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.filter-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e2e8f0;
    font-weight: 600;
    color: #2d3748;
}

.filter-content {
    padding: 20px;
}

.filter-group {
    margin-bottom: 25px;
}

.filter-group:last-child {
    margin-bottom: 0;
}

.filter-title {
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 10px;
}

.filter-options {
    list-style: none;
    margin: 0;
    padding: 0;
}

.filter-options li {
    margin-bottom: 8px;
}

.filter-options label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 13px;
    color: #4a5568;
    transition: color 0.3s ease;
}

.filter-options label:hover {
    color: #e53e3e;
}

.filter-options input[type="checkbox"] {
    margin-right: 8px;
    accent-color: #e53e3e;
}

/* 排序和视图控制 */
.products-controls {
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.sort-options {
    display: flex;
    align-items: center;
    gap: 10px;
}

.sort-btn {
    padding: 8px 15px;
    border: 1px solid #e2e8f0;
    background: white;
    color: #4a5568;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.3s ease;
}

.sort-btn:hover,
.sort-btn.active {
    background: #e53e3e;
    color: white;
    border-color: #e53e3e;
}

.view-toggle {
    display: flex;
    gap: 5px;
}

.view-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #e2e8f0;
    background: white;
    color: #4a5568;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.view-btn:hover,
.view-btn.active {
    background: #e53e3e;
    color: white;
    border-color: #e53e3e;
}

/* 产品网格视图 */
.products-grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 产品列表视图 */
.products-list-view {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 30px;
}

.product-card-list {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    display: flex;
    transition: all 0.3s ease;
}

.product-card-list:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.product-card-list .product-image {
    width: 200px;
    height: 150px;
    flex-shrink: 0;
}

.product-card-list .product-info {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.product-card-list .product-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
    height: auto;
}

.product-description {
    color: #718096;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    color: #718096;
}

.stars {
    color: #ffd700;
}

.product-sales {
    font-size: 12px;
    color: #718096;
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 40px 0;
}

.pagination-btn {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    background: white;
    color: #4a5568;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.pagination-btn:hover {
    background: #f8f9fa;
    border-color: #cbd5e0;
}

.pagination-btn.active {
    background: #e53e3e;
    color: white;
    border-color: #e53e3e;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    font-size: 14px;
    color: #718096;
    margin: 0 15px;
}

/* 产品详情页 */
.product-detail {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

.product-detail-content {
    display: flex;
    gap: 30px;
    padding: 30px;
}

.product-gallery {
    flex: 1;
    max-width: 500px;
}

.main-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 15px;
}

.thumbnail-list {
    display: flex;
    gap: 10px;
    overflow-x: auto;
}

.thumbnail {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: border-color 0.3s ease;
}

.thumbnail:hover,
.thumbnail.active {
    border-color: #e53e3e;
}

.product-details {
    flex: 1;
}

.product-detail-title {
    font-size: 24px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 15px;
    line-height: 1.4;
}

.product-detail-price {
    margin-bottom: 20px;
}

.current-price {
    font-size: 28px;
    font-weight: bold;
    color: #e53e3e;
    margin-right: 15px;
}

.original-price {
    font-size: 18px;
    color: #a0aec0;
    text-decoration: line-through;
}

.product-specs {
    margin-bottom: 25px;
}

.spec-item {
    display: flex;
    margin-bottom: 10px;
    font-size: 14px;
}

.spec-label {
    width: 80px;
    color: #718096;
    flex-shrink: 0;
}

.spec-value {
    color: #2d3748;
}

.quantity-selector {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 25px;
}

.quantity-controls {
    display: flex;
    align-items: center;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
}

.quantity-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: #f8f9fa;
    color: #4a5568;
    cursor: pointer;
    transition: background 0.3s ease;
}

.quantity-btn:hover {
    background: #e2e8f0;
}

.quantity-input {
    width: 60px;
    height: 32px;
    border: none;
    text-align: center;
    font-size: 14px;
    outline: none;
}

.product-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
}

.btn-large {
    padding: 12px 30px;
    font-size: 16px;
    font-weight: 600;
}

/* 产品详情标签页 */
.product-tabs {
    border-top: 1px solid #e2e8f0;
}

.tab-headers {
    display: flex;
    background: #f8f9fa;
}

.tab-header {
    padding: 15px 25px;
    background: none;
    border: none;
    color: #4a5568;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.tab-header:hover,
.tab-header.active {
    color: #e53e3e;
    border-bottom-color: #e53e3e;
    background: white;
}

.tab-content {
    padding: 30px;
    display: none;
}

.tab-content.active {
    display: block;
}

/* 推荐产品 */
.recommended-products {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    padding: 30px;
}

.recommended-title {
    font-size: 20px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 20px;
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .products-page {
        flex-direction: column;
    }
    
    .products-sidebar {
        width: 100%;
    }
    
    .products-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .products-grid-view {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .product-card-list {
        flex-direction: column;
    }
    
    .product-card-list .product-image {
        width: 100%;
        height: 200px;
    }
    
    .product-detail-content {
        flex-direction: column;
        padding: 20px;
    }
    
    .product-gallery {
        max-width: 100%;
    }
    
    .product-actions {
        flex-direction: column;
    }
    
    .tab-headers {
        overflow-x: auto;
    }
}

