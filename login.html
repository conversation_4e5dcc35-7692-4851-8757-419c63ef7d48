<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - e商城</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/auth.css">
</head>
<body>
    <div class="auth-page">
        <div class="auth-container">
            <!-- 左侧装饰区域 -->
            <div class="auth-decoration" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                <div class="decoration-content">
                    <div class="decoration-icon">🎈</div>
                    <h2 class="decoration-title">向钱看向厚赚</h2>
                    <p class="decoration-subtitle">第二季<br>五一你假人体钱不休</p>
                </div>
            </div>

            <!-- 右侧登录表单 -->
            <div class="auth-form-section">
                <div class="auth-header">
                    <div class="auth-logo">商城</div>
                    <h1 class="auth-title">e商城会员</h1>
                    <p class="auth-subtitle">
                        <a href="register.html" style="color: #e53e3e; text-decoration: none;">立即注册</a>
                    </p>
                </div>

                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 10px; margin-bottom: 20px; font-size: 12px; color: #856404;">
                    ⚠️ 公共场所不建议自动登录，以防账号丢失
                </div>

                <form class="auth-form" id="loginForm">
                    <div class="form-group">
                        <input type="text" class="form-input" placeholder="mcneil" value="mcneil" id="loginUsername" required>
                    </div>

                    <div class="form-group">
                        <div class="password-input-group">
                            <input type="password" class="form-input" placeholder="••••••" id="loginPassword" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('loginPassword')">👁️</button>
                        </div>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="autoLogin">
                        <label for="autoLogin">自动登录</label>
                    </div>

                    <button type="submit" class="auth-btn">登 录</button>
                </form>

                <div class="auth-links">
                    <span>使用合作网站账号登录：</span><br>
                    <a href="#" onclick="socialLogin('sina')">新浪微博</a>
                    <span class="auth-divider">|</span>
                    <a href="#" onclick="socialLogin('qq')">QQ</a>
                </div>

                <div class="social-login">
                    <div class="social-title">快速登录</div>
                    <div class="social-buttons">
                        <button class="social-btn" onclick="socialLogin('wechat')">
                            <span>💬</span> 微信登录
                        </button>
                        <button class="social-btn" onclick="socialLogin('phone')">
                            <span>📱</span> 手机登录
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录弹窗模板 -->
    <div class="login-modal" id="loginModal">
        <div class="login-modal-content">
            <button class="modal-close" onclick="closeLoginModal()">×</button>
            <div class="auth-header">
                <div class="auth-logo">商城</div>
                <h2 class="auth-title" style="font-size: 20px;">快速登录</h2>
            </div>
            
            <form class="auth-form" id="modalLoginForm">
                <div class="form-group">
                    <input type="text" class="form-input" placeholder="mcneil" value="mcneil" id="modalUsername" required>
                </div>

                <div class="form-group">
                    <div class="password-input-group">
                        <input type="password" class="form-input" placeholder="••••••" id="modalPassword" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('modalPassword')">👁️</button>
                    </div>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="modalRemember">
                    <label for="modalRemember">记住我</label>
                </div>

                <button type="submit" class="auth-btn">登录</button>
            </form>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script src="js/auth.js"></script>
</body>
</html>

