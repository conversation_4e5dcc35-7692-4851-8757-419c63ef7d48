using System.ComponentModel.DataAnnotations;

namespace ShoppingMall.Web.Models;

public class Product
{
    public int Id { get; set; }

    [Required(ErrorMessage = "请输入商品名称")]
    [StringLength(100, ErrorMessage = "商品名称不能超过100个字符")]
    public string Name { get; set; } = null!;

    [Required(ErrorMessage = "请输入商品描述")]
    public string Description { get; set; } = null!;

    [Required(ErrorMessage = "请输入商品价格")]
    [Range(0.01, double.MaxValue, ErrorMessage = "商品价格必须大于0")]
    public decimal Price { get; set; }

    public decimal? OriginalPrice { get; set; }

    [Required(ErrorMessage = "请输入商品图片URL")]
    public string ImageUrl { get; set; } = null!;

    public List<string> AdditionalImages { get; set; } = new();

    [Required(ErrorMessage = "请输入商品库存")]
    [Range(0, int.MaxValue, ErrorMessage = "库存不能为负数")]
    public int Stock { get; set; }

    public bool IsAvailable { get; set; } = true;

    public bool IsHot { get; set; }

    [Required(ErrorMessage = "请选择商品分类")]
    public string Category { get; set; } = null!;

    [Required(ErrorMessage = "请选择商品品牌")]
    public string Brand { get; set; } = null!;

    public string? Specifications { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public int ViewCount { get; set; }

    public int SalesCount { get; set; }

    public List<Review> Reviews { get; set; } = new();

    public double AverageRating => Reviews.Any() ? Reviews.Average(r => r.Rating) : 0;

    public int ReviewCount => Reviews.Count;

    public bool HasDiscount => OriginalPrice.HasValue && Price < OriginalPrice.Value;

    public int DiscountPercentage => HasDiscount ? 
        (int)Math.Round((1 - Price / OriginalPrice!.Value) * 100) : 0;
} 