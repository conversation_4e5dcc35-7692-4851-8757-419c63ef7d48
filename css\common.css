/* 通用样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #2d3748;
    background-color: #f7fafc;
}

/* 容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 头部样式 */
.header {
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.top-bar {
    background: #f8f9fa;
    padding: 8px 0;
    font-size: 12px;
    border-bottom: 1px solid #e2e8f0;
}

.top-bar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.top-links a {
    color: #718096;
    text-decoration: none;
    margin-right: 15px;
    transition: color 0.3s ease;
}

.top-links a:hover {
    color: #e53e3e;
}

.cart-info {
    color: #718096;
}

.cart-count {
    background: #e53e3e;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 10px;
    margin-left: 5px;
}

/* 主头部 */
.main-header {
    padding: 15px 0;
}

.main-header .container {
    display: flex;
    align-items: center;
    gap: 30px;
}

.logo {
    font-size: 28px;
    font-weight: bold;
    color: #e53e3e;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.logo::before {
    content: "e";
    background: #e53e3e;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    font-weight: bold;
}

.search-box {
    flex: 1;
    max-width: 500px;
    position: relative;
}

.search-input {
    width: 100%;
    padding: 12px 50px 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 25px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s ease;
}

.search-input:focus {
    border-color: #e53e3e;
}

.search-btn {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: #e53e3e;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-btn:hover {
    background: #c53030;
}

/* 导航菜单 */
.nav-menu {
    background: #e53e3e;
    padding: 0;
}

.nav-menu .container {
    display: flex;
    align-items: center;
}

.category-dropdown {
    background: #c53030;
    color: white;
    padding: 15px 20px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    position: relative;
}

.category-dropdown:hover {
    background: #a02626;
}

.main-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.main-nav li {
    margin: 0;
}

.main-nav a {
    display: block;
    color: white;
    text-decoration: none;
    padding: 15px 20px;
    transition: background 0.3s ease;
}

.main-nav a:hover {
    background: rgba(255,255,255,0.1);
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #e53e3e;
    color: white;
}

.btn-primary:hover {
    background: #c53030;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #edf2f7;
    color: #4a5568;
}

.btn-secondary:hover {
    background: #e2e8f0;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #4a5568;
}

.form-input {
    width: 100%;
    padding: 10px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 5px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s ease;
}

.form-input:focus {
    border-color: #e53e3e;
}

/* 价格样式 */
.price {
    color: #e53e3e;
    font-weight: bold;
    font-size: 18px;
}

.price-old {
    color: #a0aec0;
    text-decoration: line-through;
    font-size: 14px;
    margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-header .container {
        flex-direction: column;
        gap: 15px;
    }
    
    .search-box {
        max-width: 100%;
    }
    
    .main-nav {
        flex-wrap: wrap;
    }
    
    .container {
        padding: 0 15px;
    }
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-10 { margin-bottom: 10px; }
.mb-20 { margin-bottom: 20px; }
.mb-30 { margin-bottom: 30px; }
.mt-10 { margin-top: 10px; }
.mt-20 { margin-top: 20px; }
.mt-30 { margin-top: 30px; }
.p-10 { padding: 10px; }
.p-20 { padding: 20px; }
.p-30 { padding: 30px; }

/* 动画 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

