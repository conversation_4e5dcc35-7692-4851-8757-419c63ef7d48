// 产品页面JavaScript功能

// 当前视图模式
let currentView = 'grid';

// 排序产品
function sortProducts(sortType) {
    // 移除所有排序按钮的活动状态
    document.querySelectorAll('.sort-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // 添加当前按钮的活动状态
    event.target.classList.add('active');
    
    // 这里可以添加实际的排序逻辑
    showNotification(`按${getSortTypeName(sortType)}排序`, 'info');
}

// 获取排序类型名称
function getSortTypeName(sortType) {
    const names = {
        'default': '综合',
        'sales': '销量',
        'price': '价格',
        'views': '浏览量'
    };
    return names[sortType] || '综合';
}

// 切换视图模式
function switchView(viewType) {
    const container = document.getElementById('productsContainer');
    const viewBtns = document.querySelectorAll('.view-btn');
    
    // 更新按钮状态
    viewBtns.forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    if (viewType === 'list') {
        container.className = 'products-list-view';
        convertToListView();
        currentView = 'list';
    } else {
        container.className = 'products-grid-view';
        convertToGridView();
        currentView = 'grid';
    }
}

// 转换为列表视图
function convertToListView() {
    const container = document.getElementById('productsContainer');
    const products = container.querySelectorAll('.product-card');
    
    products.forEach(product => {
        product.classList.add('product-card-list');
        
        // 添加产品描述
        const productInfo = product.querySelector('.product-info');
        const title = productInfo.querySelector('.product-title');
        
        if (!productInfo.querySelector('.product-description')) {
            const description = document.createElement('div');
            description.className = 'product-description';
            description.textContent = '高性能台式机，适合办公和娱乐使用，配置均衡，性价比高。';
            title.insertAdjacentElement('afterend', description);
        }
        
        // 添加评分和销量信息
        const price = productInfo.querySelector('.product-price');
        if (!productInfo.querySelector('.product-meta')) {
            const meta = document.createElement('div');
            meta.className = 'product-meta';
            meta.innerHTML = `
                <div class="product-rating">
                    <span class="stars">★★★★☆</span>
                    <span>4.5分</span>
                </div>
                <div class="product-sales">已售${Math.floor(Math.random() * 100) + 10}件</div>
            `;
            price.insertAdjacentElement('afterend', meta);
        }
    });
}

// 转换为网格视图
function convertToGridView() {
    const container = document.getElementById('productsContainer');
    const products = container.querySelectorAll('.product-card');
    
    products.forEach(product => {
        product.classList.remove('product-card-list');
        
        // 移除列表视图特有的元素
        const description = product.querySelector('.product-description');
        const meta = product.querySelector('.product-meta');
        
        if (description) description.remove();
        if (meta) meta.remove();
    });
}

// 筛选功能
function initializeFilters() {
    const checkboxes = document.querySelectorAll('.filter-options input[type="checkbox"]');
    
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            applyFilters();
        });
    });
}

// 应用筛选
function applyFilters() {
    const selectedFilters = [];
    const checkboxes = document.querySelectorAll('.filter-options input[type="checkbox"]:checked');
    
    checkboxes.forEach(checkbox => {
        selectedFilters.push(checkbox.parentElement.textContent.trim());
    });
    
    if (selectedFilters.length > 0) {
        showNotification(`已选择筛选条件：${selectedFilters.join(', ')}`, 'info');
    }
    
    // 这里可以添加实际的筛选逻辑
    filterProducts(selectedFilters);
}

// 筛选产品
function filterProducts(filters) {
    // 模拟筛选逻辑
    console.log('应用筛选条件:', filters);
    
    // 实际项目中，这里会向服务器发送请求或在前端进行筛选
}

// 分页功能
function initializePagination() {
    const paginationBtns = document.querySelectorAll('.pagination-btn');
    
    paginationBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            if (!this.disabled && this.textContent.includes('页')) {
                const pageInput = document.querySelector('input[type="number"]');
                const pageNumber = parseInt(pageInput.value);
                
                if (pageNumber && pageNumber > 0) {
                    goToPage(pageNumber);
                }
            }
        });
    });
}

// 跳转到指定页面
function goToPage(pageNumber) {
    showNotification(`跳转到第${pageNumber}页`, 'info');
    
    // 滚动到顶部
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
    
    // 这里可以添加实际的分页逻辑
}

// 产品卡片悬停效果
function initializeProductHoverEffects() {
    const productCards = document.querySelectorAll('.product-card');
    
    productCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
        });
    });
}

// 懒加载产品图片
function initializeLazyLoading() {
    const images = document.querySelectorAll('.product-image img');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.style.opacity = '0';
                img.style.transition = 'opacity 0.3s ease';
                
                img.onload = function() {
                    this.style.opacity = '1';
                };
                
                observer.unobserve(img);
            }
        });
    });
    
    images.forEach(img => {
        imageObserver.observe(img);
    });
}

// 搜索功能增强
function enhanceSearch() {
    const searchInput = document.querySelector('.search-input');
    
    if (searchInput) {
        // 搜索建议功能
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            if (query.length > 2) {
                showSearchSuggestions(query);
            } else {
                hideSearchSuggestions();
            }
        });
        
        // 点击外部隐藏建议
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.search-box')) {
                hideSearchSuggestions();
            }
        });
    }
}

// 显示搜索建议
function showSearchSuggestions(query) {
    // 模拟搜索建议
    const suggestions = [
        '主机箱',
        '主板',
        '主机电源',
        '主机散热器'
    ].filter(item => item.includes(query));
    
    if (suggestions.length > 0) {
        createSuggestionsList(suggestions);
    }
}

// 创建建议列表
function createSuggestionsList(suggestions) {
    // 移除现有建议列表
    const existingList = document.querySelector('.search-suggestions');
    if (existingList) {
        existingList.remove();
    }
    
    const searchBox = document.querySelector('.search-box');
    const suggestionsList = document.createElement('div');
    suggestionsList.className = 'search-suggestions';
    suggestionsList.style.cssText = `
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #e2e8f0;
        border-top: none;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        z-index: 1000;
        max-height: 200px;
        overflow-y: auto;
    `;
    
    suggestions.forEach(suggestion => {
        const item = document.createElement('div');
        item.className = 'suggestion-item';
        item.textContent = suggestion;
        item.style.cssText = `
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 1px solid #f1f5f9;
            transition: background 0.2s ease;
        `;
        
        item.addEventListener('mouseenter', function() {
            this.style.background = '#f8f9fa';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.background = 'white';
        });
        
        item.addEventListener('click', function() {
            document.querySelector('.search-input').value = suggestion;
            hideSearchSuggestions();
            performSearch();
        });
        
        suggestionsList.appendChild(item);
    });
    
    searchBox.appendChild(suggestionsList);
}

// 隐藏搜索建议
function hideSearchSuggestions() {
    const suggestionsList = document.querySelector('.search-suggestions');
    if (suggestionsList) {
        suggestionsList.remove();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeFilters();
    initializePagination();
    initializeProductHoverEffects();
    initializeLazyLoading();
    enhanceSearch();
    
    // 添加页面加载动画
    const productCards = document.querySelectorAll('.product-card');
    productCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});

