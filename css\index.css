/* 首页样式 */

/* 主要内容区域 */
.main-content {
    display: flex;
    gap: 20px;
    margin: 20px 0;
}

/* 左侧分类菜单 */
.sidebar {
    width: 250px;
    flex-shrink: 0;
}

.category-menu {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
}

.category-menu h3 {
    background: #e53e3e;
    color: white;
    padding: 15px 20px;
    margin: 0;
    font-size: 16px;
}

.category-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.category-list li {
    border-bottom: 1px solid #f1f5f9;
}

.category-list li:last-child {
    border-bottom: none;
}

.category-list a {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px;
    color: #4a5568;
    text-decoration: none;
    transition: all 0.3s ease;
}

.category-list a:hover {
    background: #f8f9fa;
    color: #e53e3e;
    padding-left: 25px;
}

.category-list .subcategories {
    font-size: 12px;
    color: #a0aec0;
}

/* 主内容区 */
.content-area {
    flex: 1;
}

/* 轮播图区域 */
.hero-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 30px;
    position: relative;
}

.carousel {
    position: relative;
    height: 400px;
    overflow: hidden;
}

.carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.carousel-slide.active {
    opacity: 1;
}

.carousel-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.carousel-indicators {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
}

.carousel-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255,255,255,0.5);
    border: none;
    cursor: pointer;
    transition: background 0.3s ease;
}

.carousel-indicator.active {
    background: white;
}

.carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0,0,0,0.5);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    transition: background 0.3s ease;
}

.carousel-nav:hover {
    background: rgba(0,0,0,0.7);
}

.carousel-prev {
    left: 20px;
}

.carousel-next {
    right: 20px;
}

/* 产品展示区域 */
.products-section {
    margin-bottom: 40px;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e53e3e;
}

.section-title {
    font-size: 24px;
    font-weight: bold;
    color: #2d3748;
    margin: 0;
}

.section-more {
    color: #e53e3e;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.section-more:hover {
    color: #c53030;
}

/* 产品网格 */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.product-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.product-image {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #e53e3e;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.product-info {
    padding: 15px;
}

.product-title {
    font-size: 14px;
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 8px;
    line-height: 1.4;
    height: 40px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-price {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.current-price {
    color: #e53e3e;
    font-size: 18px;
    font-weight: bold;
}

.original-price {
    color: #a0aec0;
    font-size: 14px;
    text-decoration: line-through;
}

.product-actions {
    display: flex;
    gap: 10px;
}

.add-to-cart {
    flex: 1;
    background: #e53e3e;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.add-to-cart:hover {
    background: #c53030;
}

.add-to-wishlist {
    background: #f7fafc;
    color: #4a5568;
    border: 1px solid #e2e8f0;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-to-wishlist:hover {
    background: #edf2f7;
    color: #e53e3e;
}

/* 分类展示区域 */
.categories-showcase {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.category-showcase-item {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
}

.category-showcase-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.category-icon {
    width: 60px;
    height: 60px;
    background: #f8f9fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 24px;
    color: #e53e3e;
    transition: all 0.3s ease;
}

.category-showcase-item:hover .category-icon {
    background: #e53e3e;
    color: white;
}

.category-name {
    font-size: 14px;
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 5px;
}

.category-count {
    font-size: 12px;
    color: #a0aec0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .categories-showcase {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
    }
    
    .carousel {
        height: 250px;
    }
}

