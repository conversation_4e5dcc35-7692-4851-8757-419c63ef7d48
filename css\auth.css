/* 登录注册页面样式 */

.auth-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
}

.auth-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
    overflow: hidden;
    max-width: 900px;
    width: 100%;
    display: flex;
    min-height: 500px;
}

/* 左侧装饰区域 */
.auth-decoration {
    flex: 1;
    background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.auth-decoration::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translate(0, 0) rotate(0deg); }
    100% { transform: translate(-50px, -50px) rotate(360deg); }
}

.decoration-content {
    text-align: center;
    color: white;
    z-index: 1;
    position: relative;
}

.decoration-icon {
    width: 120px;
    height: 120px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 48px;
    backdrop-filter: blur(10px);
}

.decoration-title {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.decoration-subtitle {
    font-size: 16px;
    opacity: 0.9;
    line-height: 1.5;
}

/* 右侧表单区域 */
.auth-form-section {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-logo {
    font-size: 32px;
    font-weight: bold;
    color: #e53e3e;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.auth-logo::before {
    content: "e";
    background: #e53e3e;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.auth-title {
    font-size: 24px;
    color: #2d3748;
    margin-bottom: 8px;
}

.auth-subtitle {
    color: #718096;
    font-size: 14px;
}

/* 表单样式 */
.auth-form {
    width: 100%;
}

.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-input:focus {
    border-color: #e53e3e;
    background: white;
    box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.form-input::placeholder {
    color: #a0aec0;
}

/* 密码输入框 */
.password-input-group {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #718096;
    cursor: pointer;
    font-size: 16px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #e53e3e;
}

/* 验证码 */
.captcha-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.captcha-input {
    flex: 1;
}

.captcha-image {
    width: 100px;
    height: 40px;
    background: #f0f0f0;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: monospace;
    font-weight: bold;
    color: #e53e3e;
    cursor: pointer;
    user-select: none;
    transition: all 0.3s ease;
}

.captcha-image:hover {
    background: #e2e8f0;
}

.captcha-refresh {
    color: #718096;
    font-size: 12px;
    text-decoration: none;
    transition: color 0.3s ease;
}

.captcha-refresh:hover {
    color: #e53e3e;
}

/* 复选框 */
.checkbox-group {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 8px;
    accent-color: #e53e3e;
}

.checkbox-group label {
    font-size: 13px;
    color: #4a5568;
    cursor: pointer;
}

.checkbox-group a {
    color: #e53e3e;
    text-decoration: none;
}

.checkbox-group a:hover {
    text-decoration: underline;
}

/* 按钮 */
.auth-btn {
    width: 100%;
    padding: 12px;
    background: #e53e3e;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.auth-btn:hover {
    background: #c53030;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3);
}

.auth-btn:active {
    transform: translateY(0);
}

.auth-btn:disabled {
    background: #cbd5e0;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 链接 */
.auth-links {
    text-align: center;
    font-size: 14px;
}

.auth-links a {
    color: #e53e3e;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.auth-links a:hover {
    color: #c53030;
    text-decoration: underline;
}

.auth-divider {
    margin: 0 10px;
    color: #a0aec0;
}

/* 社交登录 */
.social-login {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
}

.social-title {
    text-align: center;
    color: #718096;
    font-size: 14px;
    margin-bottom: 15px;
}

.social-buttons {
    display: flex;
    gap: 10px;
}

.social-btn {
    flex: 1;
    padding: 10px;
    border: 1px solid #e2e8f0;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 14px;
    color: #4a5568;
}

.social-btn:hover {
    background: #f8f9fa;
    border-color: #cbd5e0;
}

/* 登录弹窗样式 */
.login-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.login-modal.active {
    opacity: 1;
    visibility: visible;
}

.login-modal-content {
    background: white;
    border-radius: 12px;
    padding: 30px;
    width: 90%;
    max-width: 400px;
    transform: translateY(-20px);
    transition: transform 0.3s ease;
}

.login-modal.active .login-modal-content {
    transform: translateY(0);
}

.modal-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 20px;
    color: #718096;
    cursor: pointer;
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: #e53e3e;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .auth-container {
        flex-direction: column;
        max-width: 400px;
        margin: 20px;
    }
    
    .auth-decoration {
        min-height: 200px;
    }
    
    .decoration-icon {
        width: 80px;
        height: 80px;
        font-size: 32px;
    }
    
    .decoration-title {
        font-size: 24px;
    }
    
    .decoration-subtitle {
        font-size: 14px;
    }
    
    .auth-form-section {
        padding: 30px 20px;
    }
    
    .social-buttons {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .auth-page {
        padding: 10px;
    }
    
    .auth-container {
        margin: 10px;
    }
    
    .auth-form-section {
        padding: 20px 15px;
    }
    
    .captcha-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .captcha-image {
        width: 100%;
        margin-top: 10px;
    }
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-form-section > * {
    animation: slideInUp 0.6s ease-out;
}

.auth-form-section > *:nth-child(2) { animation-delay: 0.1s; }
.auth-form-section > *:nth-child(3) { animation-delay: 0.2s; }
.auth-form-section > *:nth-child(4) { animation-delay: 0.3s; }

