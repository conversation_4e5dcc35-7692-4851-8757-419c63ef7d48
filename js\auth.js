// 登录注册页面JavaScript功能

// 切换密码显示/隐藏
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;
    
    if (input.type === 'password') {
        input.type = 'text';
        button.textContent = '🙈';
    } else {
        input.type = 'password';
        button.textContent = '👁️';
    }
}

// 刷新验证码
function refreshCaptcha() {
    const captchaImage = document.getElementById('captchaImage');
    const codes = ['H4LR', 'K9M2', 'P7X3', 'Q5N8', 'R2Y6', 'T4W9'];
    const randomCode = codes[Math.floor(Math.random() * codes.length)];
    
    captchaImage.textContent = randomCode;
    captchaImage.style.animation = 'none';
    setTimeout(() => {
        captchaImage.style.animation = 'pulse 0.5s ease';
    }, 10);
}

// 验证表单
function validateForm(formType) {
    if (formType === 'register') {
        return validateRegisterForm();
    } else {
        return validateLoginForm();
    }
}

// 验证注册表单
function validateRegisterForm() {
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const captcha = document.getElementById('captcha').value.trim();
    const agreeTerms = document.getElementById('agreeTerms').checked;
    
    // 用户名验证
    if (username.length < 3) {
        showNotification('用户名至少需要3个字符！', 'error');
        return false;
    }
    
    if (!/^[a-zA-Z0-9_]+$/.test(username)) {
        showNotification('用户名只能包含字母、数字和下划线！', 'error');
        return false;
    }
    
    // 密码验证
    if (password.length < 6) {
        showNotification('密码至少需要6个字符！', 'error');
        return false;
    }
    
    if (password !== confirmPassword) {
        showNotification('两次输入的密码不一致！', 'error');
        return false;
    }
    
    // 验证码验证
    if (captcha.length !== 4) {
        showNotification('请输入4位验证码！', 'error');
        return false;
    }
    
    // 协议验证
    if (!agreeTerms) {
        showNotification('请阅读并同意用户注册协议！', 'error');
        return false;
    }
    
    return true;
}

// 验证登录表单
function validateLoginForm() {
    const username = document.getElementById('loginUsername').value.trim();
    const password = document.getElementById('loginPassword').value;
    
    if (!username) {
        showNotification('请输入用户名！', 'error');
        return false;
    }
    
    if (!password) {
        showNotification('请输入密码！', 'error');
        return false;
    }
    
    return true;
}

// 处理注册
function handleRegister(event) {
    event.preventDefault();
    
    if (!validateForm('register')) {
        return;
    }
    
    const submitBtn = event.target.querySelector('.auth-btn');
    const originalText = submitBtn.textContent;
    
    // 显示加载状态
    submitBtn.disabled = true;
    submitBtn.textContent = '注册中...';
    
    // 模拟注册请求
    setTimeout(() => {
        const username = document.getElementById('username').value.trim();
        
        // 模拟注册成功
        showNotification('注册成功！正在跳转到登录页面...', 'success');
        
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 1500);
        
    }, 2000);
}

// 处理登录
function handleLogin(event) {
    event.preventDefault();
    
    const form = event.target;
    const isModal = form.id === 'modalLoginForm';
    const usernameId = isModal ? 'modalUsername' : 'loginUsername';
    const passwordId = isModal ? 'modalPassword' : 'loginPassword';
    
    const username = document.getElementById(usernameId).value.trim();
    const password = document.getElementById(passwordId).value;
    
    if (!username || !password) {
        showNotification('请输入用户名和密码！', 'error');
        return;
    }
    
    const submitBtn = form.querySelector('.auth-btn');
    const originalText = submitBtn.textContent;
    
    // 显示加载状态
    submitBtn.disabled = true;
    submitBtn.textContent = '登录中...';
    
    // 模拟登录请求
    setTimeout(() => {
        // 模拟登录成功
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem('username', username);
        
        showNotification('登录成功！正在跳转...', 'success');
        
        setTimeout(() => {
            if (isModal) {
                closeLoginModal();
                location.reload();
            } else {
                window.location.href = 'index.html';
            }
        }, 1500);
        
    }, 2000);
}

// 社交登录
function socialLogin(platform) {
    const platformNames = {
        'wechat': '微信',
        'qq': 'QQ',
        'sina': '新浪微博',
        'phone': '手机'
    };
    
    showNotification(`${platformNames[platform]}登录功能开发中...`, 'info');
}

// 社交注册
function socialRegister(platform) {
    const platformNames = {
        'wechat': '微信',
        'qq': 'QQ'
    };
    
    showNotification(`${platformNames[platform]}注册功能开发中...`, 'info');
}

// 打开登录弹窗
function openLoginModal() {
    const modal = document.getElementById('loginModal');
    if (modal) {
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
}

// 关闭登录弹窗
function closeLoginModal() {
    const modal = document.getElementById('loginModal');
    if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = '';
    }
}

// 密码强度检测
function checkPasswordStrength(password) {
    let strength = 0;
    const checks = [
        /.{8,}/, // 至少8位
        /[a-z]/, // 包含小写字母
        /[A-Z]/, // 包含大写字母
        /[0-9]/, // 包含数字
        /[^A-Za-z0-9]/ // 包含特殊字符
    ];
    
    checks.forEach(check => {
        if (check.test(password)) strength++;
    });
    
    return strength;
}

// 显示密码强度
function showPasswordStrength(inputId, strengthId) {
    const input = document.getElementById(inputId);
    const strengthIndicator = document.getElementById(strengthId);
    
    if (!input || !strengthIndicator) return;
    
    input.addEventListener('input', function() {
        const password = this.value;
        const strength = checkPasswordStrength(password);
        
        const levels = ['很弱', '弱', '一般', '强', '很强'];
        const colors = ['#ff4757', '#ff6b7a', '#ffa502', '#2ed573', '#1e90ff'];
        
        if (password.length === 0) {
            strengthIndicator.style.display = 'none';
            return;
        }
        
        strengthIndicator.style.display = 'block';
        strengthIndicator.textContent = `密码强度：${levels[strength - 1] || '很弱'}`;
        strengthIndicator.style.color = colors[strength - 1] || colors[0];
    });
}

// 自动填充演示数据
function fillDemoData() {
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirmPassword');
    
    if (usernameInput && passwordInput && confirmPasswordInput) {
        usernameInput.value = 'demo_user';
        passwordInput.value = 'demo123456';
        confirmPasswordInput.value = 'demo123456';
    }
}

// 表单自动保存
function enableAutoSave(formId) {
    const form = document.getElementById(formId);
    if (!form) return;
    
    const inputs = form.querySelectorAll('input[type="text"], input[type="password"]');
    
    inputs.forEach(input => {
        // 加载保存的数据
        const savedValue = localStorage.getItem(`autosave_${input.id}`);
        if (savedValue && input.type !== 'password') {
            input.value = savedValue;
        }
        
        // 保存输入数据
        input.addEventListener('input', function() {
            if (this.type !== 'password') {
                localStorage.setItem(`autosave_${this.id}`, this.value);
            }
        });
    });
}

// 清除自动保存的数据
function clearAutoSave() {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
        if (key.startsWith('autosave_')) {
            localStorage.removeItem(key);
        }
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 注册表单事件
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
        enableAutoSave('registerForm');
        
        // 添加密码强度检测
        const passwordInput = document.getElementById('password');
        if (passwordInput) {
            const strengthIndicator = document.createElement('div');
            strengthIndicator.id = 'passwordStrength';
            strengthIndicator.style.cssText = `
                font-size: 12px;
                margin-top: 5px;
                display: none;
            `;
            passwordInput.parentElement.appendChild(strengthIndicator);
            showPasswordStrength('password', 'passwordStrength');
        }
    }
    
    // 登录表单事件
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // 弹窗登录表单事件
    const modalLoginForm = document.getElementById('modalLoginForm');
    if (modalLoginForm) {
        modalLoginForm.addEventListener('submit', handleLogin);
    }
    
    // 弹窗外部点击关闭
    const loginModal = document.getElementById('loginModal');
    if (loginModal) {
        loginModal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeLoginModal();
            }
        });
    }
    
    // ESC键关闭弹窗
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeLoginModal();
        }
    });
    
    // 检查登录状态
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (isLoggedIn === 'true' && window.location.pathname.includes('login.html')) {
        showNotification('您已登录，正在跳转到首页...', 'info');
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 1500);
    }
    
    // 初始化验证码
    refreshCaptcha();
    
    // 添加页面加载动画
    const authContainer = document.querySelector('.auth-container');
    if (authContainer) {
        authContainer.style.opacity = '0';
        authContainer.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            authContainer.style.transition = 'all 0.6s ease';
            authContainer.style.opacity = '1';
            authContainer.style.transform = 'translateY(0)';
        }, 100);
    }
});

// 全局函数，供其他页面调用
window.openLoginModal = openLoginModal;
window.closeLoginModal = closeLoginModal;

