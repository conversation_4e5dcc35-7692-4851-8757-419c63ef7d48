using System.ComponentModel.DataAnnotations;

namespace ShoppingMall.Web.Models;

public class Order
{
    public int Id { get; set; }

    public string UserId { get; set; } = null!;

    public string UserName { get; set; } = null!;

    [Required(ErrorMessage = "请输入收货人姓名")]
    public string ReceiverName { get; set; } = null!;

    [Required(ErrorMessage = "请输入收货人电话")]
    public string ReceiverPhone { get; set; } = null!;

    [Required(ErrorMessage = "请输入收货地址")]
    public string ReceiverAddress { get; set; } = null!;

    public decimal TotalAmount { get; set; }

    public string Status { get; set; } = "待付款";

    public DateTime CreatedAt { get; set; }

    public DateTime? PaidAt { get; set; }

    public DateTime? ShippedAt { get; set; }

    public DateTime? CompletedAt { get; set; }

    public DateTime? CancelledAt { get; set; }

    public string? CancelReason { get; set; }

    public List<OrderItem> Items { get; set; } = new();
} 