// 轮播图功能

class Carousel {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.slides = this.container.querySelectorAll('.carousel-slide');
        this.indicators = this.container.querySelectorAll('.carousel-indicator');
        this.currentSlide = 0;
        this.isPlaying = true;
        this.interval = null;
        
        this.init();
    }
    
    init() {
        if (this.slides.length === 0) return;
        
        // 开始自动播放
        this.startAutoPlay();
        
        // 鼠标悬停时暂停
        this.container.addEventListener('mouseenter', () => {
            this.stopAutoPlay();
        });
        
        // 鼠标离开时继续播放
        this.container.addEventListener('mouseleave', () => {
            this.startAutoPlay();
        });
        
        // 键盘导航
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') {
                this.prevSlide();
            } else if (e.key === 'ArrowRight') {
                this.nextSlide();
            }
        });
        
        // 触摸支持
        this.addTouchSupport();
    }
    
    goToSlide(index) {
        if (index < 0 || index >= this.slides.length) return;
        
        // 移除当前活动状态
        this.slides[this.currentSlide].classList.remove('active');
        this.indicators[this.currentSlide].classList.remove('active');
        
        // 设置新的活动状态
        this.currentSlide = index;
        this.slides[this.currentSlide].classList.add('active');
        this.indicators[this.currentSlide].classList.add('active');
        
        // 添加切换动画效果
        this.slides[this.currentSlide].style.animation = 'slideIn 0.5s ease-in-out';
        setTimeout(() => {
            this.slides[this.currentSlide].style.animation = '';
        }, 500);
    }
    
    nextSlide() {
        const nextIndex = (this.currentSlide + 1) % this.slides.length;
        this.goToSlide(nextIndex);
    }
    
    prevSlide() {
        const prevIndex = (this.currentSlide - 1 + this.slides.length) % this.slides.length;
        this.goToSlide(prevIndex);
    }
    
    startAutoPlay() {
        if (this.interval) return;
        
        this.interval = setInterval(() => {
            this.nextSlide();
        }, 4000); // 4秒切换一次
    }
    
    stopAutoPlay() {
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }
    }
    
    addTouchSupport() {
        let startX = 0;
        let endX = 0;
        
        this.container.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
        });
        
        this.container.addEventListener('touchend', (e) => {
            endX = e.changedTouches[0].clientX;
            this.handleSwipe();
        });
        
        const handleSwipe = () => {
            const threshold = 50; // 最小滑动距离
            const diff = startX - endX;
            
            if (Math.abs(diff) > threshold) {
                if (diff > 0) {
                    // 向左滑动，显示下一张
                    this.nextSlide();
                } else {
                    // 向右滑动，显示上一张
                    this.prevSlide();
                }
            }
        };
        
        this.handleSwipe = handleSwipe;
    }
}

// 全局函数，供HTML调用
function goToSlide(index) {
    if (window.mainCarousel) {
        window.mainCarousel.goToSlide(index);
    }
}

function nextSlide() {
    if (window.mainCarousel) {
        window.mainCarousel.nextSlide();
    }
}

function prevSlide() {
    if (window.mainCarousel) {
        window.mainCarousel.prevSlide();
    }
}

// 页面加载完成后初始化轮播图
document.addEventListener('DOMContentLoaded', function() {
    // 添加轮播图切换动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }
        
        .carousel-slide {
            transition: opacity 0.5s ease-in-out;
        }
        
        .carousel-indicator {
            transition: all 0.3s ease;
        }
        
        .carousel-nav {
            transition: all 0.3s ease;
            opacity: 0.7;
        }
        
        .carousel:hover .carousel-nav {
            opacity: 1;
        }
        
        .carousel-nav:hover {
            transform: translateY(-50%) scale(1.1);
        }
    `;
    document.head.appendChild(style);
    
    // 初始化主轮播图
    if (document.getElementById('mainCarousel')) {
        window.mainCarousel = new Carousel('mainCarousel');
    }
    
    // 预加载轮播图片
    preloadCarouselImages();
});

// 预加载轮播图片
function preloadCarouselImages() {
    const slides = document.querySelectorAll('.carousel-slide img');
    slides.forEach(img => {
        const image = new Image();
        image.src = img.src;
    });
}

// 轮播图性能优化
function optimizeCarousel() {
    const carousel = document.getElementById('mainCarousel');
    if (!carousel) return;
    
    // 使用 Intersection Observer 来优化性能
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // 轮播图进入视口时开始播放
                if (window.mainCarousel) {
                    window.mainCarousel.startAutoPlay();
                }
            } else {
                // 轮播图离开视口时暂停播放
                if (window.mainCarousel) {
                    window.mainCarousel.stopAutoPlay();
                }
            }
        });
    });
    
    observer.observe(carousel);
}

// 在页面加载完成后进行性能优化
window.addEventListener('load', optimizeCarousel);

