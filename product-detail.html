<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欣睿宇(XXY) 酷睿i3 4160/4G/1TB/独显/台式机 - e商城</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/products.css">
</head>
<body>
    <!-- 头部 -->
    <header class="header">
        <!-- 顶部栏 -->
        <div class="top-bar">
            <div class="container">
                <div class="top-links">
                    <a href="#" onclick="addToFavorites()">⭐ 收藏本站</a>
                    <a href="login.html">登录</a>
                    <a href="profile.html">我的订单</a>
                    <a href="#">手机版</a>
                    <a href="#">客户服务</a>
                </div>
                <div class="cart-info">
                    <a href="#" style="color: inherit; text-decoration: none;">
                        🛒 我的购物车 <span class="cart-count">0</span>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 主头部 -->
        <div class="main-header">
            <div class="container">
                <a href="index.html" class="logo">商城 <span style="font-size: 14px; color: #666;">多·好·快·省</span></a>
                
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="热门搜索：小米手机、固态硬盘、单反相机、机械键盘、空气净化器">
                    <button class="search-btn" onclick="performSearch()">搜索</button>
                </div>
            </div>
        </div>
        
        <!-- 导航菜单 -->
        <nav class="nav-menu">
            <div class="container">
                <button class="category-dropdown" onclick="toggleCategoryMenu()">
                    全部商品分类 ▼
                </button>
                <ul class="main-nav">
                    <li><a href="index.html">首页</a></li>
                    <li><a href="products.html?category=smart">智能本区</a></li>
                    <li><a href="products.html?category=group">团购</a></li>
                    <li><a href="products.html?category=mobile">手机配件</a></li>
                    <li><a href="products.html?category=flash">闪购</a></li>
                    <li><a href="products.html?category=diy">电脑DIY</a></li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- 面包屑导航 -->
    <div class="container">
        <nav class="breadcrumb">
            <ol class="breadcrumb-list">
                <li><a href="index.html">电脑配件</a></li>
                <li class="breadcrumb-separator">></li>
                <li><a href="products.html">电脑整机</a></li>
                <li class="breadcrumb-separator">></li>
                <li class="current">欣睿宇(XXY) 酷睿i3 4160/4G/1TB/独显/台式机</li>
            </ol>
        </nav>
    </div>

    <!-- 主要内容 -->
    <main class="container">
        <!-- 产品详情 -->
        <div class="product-detail">
            <div class="product-detail-content">
                <!-- 产品图片 -->
                <div class="product-gallery">
                    <img src="images/products/bxite-G2015051800550001.jpg" alt="欣睿宇(XXY) 酷睿i3 4160/4G/1TB/独显/台式机" class="main-image" id="mainImage">
                    <div class="thumbnail-list">
                        <img src="images/products/bxite-G2015051800550001.jpg" alt="主图" class="thumbnail active" onclick="changeMainImage(this)">
                        <img src="images/products/bxite-G2015051717070001.jpg" alt="侧面图" class="thumbnail" onclick="changeMainImage(this)">
                        <img src="images/products/bxite-G2015051801580001.jpg" alt="背面图" class="thumbnail" onclick="changeMainImage(this)">
                    </div>
                </div>

                <!-- 产品信息 -->
                <div class="product-details">
                    <h1 class="product-detail-title">欣睿宇(XXY) 酷睿i3 4160/4G/1TB/独显/台式机 办公电脑主机 DIY组装机 618手机专享新用户3 4160/5配置LOL CF 1TB 语音套餐，原价日常生活价</h1>
                    
                    <div class="product-detail-price">
                        <span class="current-price">¥1659.00</span>
                    </div>

                    <div class="product-specs">
                        <div class="spec-item">
                            <span class="spec-label">商品编号：</span>
                            <span class="spec-value">1202543630</span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label">运费：</span>
                            <span class="spec-value">免运费</span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label">库存：</span>
                            <span class="spec-value">由各网点配送发货，并提供售后服务</span>
                        </div>
                    </div>

                    <div class="quantity-selector">
                        <span>数量：</span>
                        <div class="quantity-controls">
                            <button class="quantity-btn" onclick="decreaseQuantity()">-</button>
                            <input type="number" class="quantity-input" value="1" min="1" max="99" id="quantityInput">
                            <button class="quantity-btn" onclick="increaseQuantity()">+</button>
                        </div>
                        <span style="margin-left: 15px; color: #718096; font-size: 14px;">
                            已选择：<span id="selectedQuantity">1</span>
                        </span>
                    </div>

                    <div class="product-actions">
                        <button class="btn btn-primary btn-large" onclick="addToCart(201)">加入购物车</button>
                        <button class="btn btn-secondary btn-large" onclick="addToWishlist(201)">收藏商品</button>
                    </div>
                </div>
            </div>

            <!-- 产品详情标签页 -->
            <div class="product-tabs">
                <div class="tab-headers">
                    <button class="tab-header active" onclick="switchTab('recommend')">热门推荐</button>
                    <button class="tab-header" onclick="switchTab('description')">商品介绍</button>
                    <button class="tab-header" onclick="switchTab('package')">包装清单</button>
                    <button class="tab-header" onclick="switchTab('reviews')">商品评价(0)</button>
                </div>

                <div class="tab-content active" id="recommend">
                    <div class="recommended-products">
                        <h3 class="recommended-title">热门推荐</h3>
                        <div class="products-grid">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="images/products/bxite-G2015051717070001.jpg" alt="神舟AMD860K/4G/2G独显120G固态硬盘台式机DIY组装机">
                                </div>
                                <div class="product-info">
                                    <h3 class="product-title">神舟AMD860K/4G/2G独显120G固态硬盘台式机DIY组装机</h3>
                                    <div class="product-price">
                                        <span class="current-price">¥1788.00</span>
                                    </div>
                                    <div class="product-actions">
                                        <button class="add-to-cart" onclick="addToCart(202)">加入购物车</button>
                                        <button class="add-to-wishlist" onclick="addToWishlist(202)">♡</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="description">
                    <div style="padding: 20px 0;">
                        <h4 style="margin-bottom: 15px; color: #2d3748;">产品名称：</h4>
                        <p style="margin-bottom: 15px; line-height: 1.6;">欣睿宇(XXY) 酷睿i3 4160/4G</p>
                        
                        <h4 style="margin-bottom: 15px; color: #2d3748;">产品编号：</h4>
                        <p style="margin-bottom: 15px; line-height: 1.6;">1202543630</p>
                        
                        <h4 style="margin-bottom: 15px; color: #2d3748;">发货地：</h4>
                        <p style="margin-bottom: 15px; line-height: 1.6;">广州</p>
                        
                        <h4 style="margin-bottom: 15px; color: #2d3748;">售后服务：</h4>
                        <p style="margin-bottom: 15px; line-height: 1.6;">由厂家提供，支持全国联保，享受三包服务，质保期为：整机1年，主要部件3年...</p>
                        
                        <h4 style="margin-bottom: 15px; color: #2d3748;">上架时间：</h4>
                        <p style="margin-bottom: 15px; line-height: 1.6;">2014-10-15 19:27:30</p>
                        
                        <h4 style="margin-bottom: 15px; color: #2d3748;">商家：</h4>
                        <p style="margin-bottom: 15px; line-height: 1.6;">深圳市小公司</p>
                        
                        <h4 style="margin-bottom: 15px; color: #2d3748;">商品毛重：</h4>
                        <p style="margin-bottom: 15px; line-height: 1.6;">8.3kg</p>
                        
                        <h4 style="margin-bottom: 15px; color: #2d3748;">CPU：</h4>
                        <p style="margin-bottom: 15px; line-height: 1.6;">酷睿i3</p>
                    </div>
                </div>

                <div class="tab-content" id="package">
                    <div style="padding: 20px 0;">
                        <h4 style="margin-bottom: 15px; color: #2d3748;">包装清单：</h4>
                        <ul style="line-height: 1.8; padding-left: 20px;">
                            <li>主机 × 1</li>
                            <li>电源线 × 1</li>
                            <li>说明书 × 1</li>
                            <li>保修卡 × 1</li>
                            <li>驱动光盘 × 1</li>
                        </ul>
                    </div>
                </div>

                <div class="tab-content" id="reviews">
                    <div style="padding: 20px 0; text-align: center; color: #718096;">
                        <p>暂无商品评价</p>
                        <p style="margin-top: 10px; font-size: 14px;">购买后可以在此处查看和发表评价</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer style="background: #2d3748; color: white; padding: 40px 0; margin-top: 60px;">
        <div class="container">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 30px;">
                <div>
                    <h4 style="margin-bottom: 15px;">关于我们</h4>
                    <p style="color: #a0aec0; font-size: 14px; line-height: 1.6;">
                        e商城致力于为用户提供优质的购物体验，多·好·快·省是我们的服务理念。
                    </p>
                </div>
                <div>
                    <h4 style="margin-bottom: 15px;">客户服务</h4>
                    <ul style="list-style: none; color: #a0aec0; font-size: 14px;">
                        <li style="margin-bottom: 8px;"><a href="#" style="color: inherit; text-decoration: none;">帮助中心</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: inherit; text-decoration: none;">售后服务</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: inherit; text-decoration: none;">配送说明</a></li>
                    </ul>
                </div>
                <div>
                    <h4 style="margin-bottom: 15px;">联系我们</h4>
                    <ul style="list-style: none; color: #a0aec0; font-size: 14px;">
                        <li style="margin-bottom: 8px;">客服热线：400-123-4567</li>
                        <li style="margin-bottom: 8px;">邮箱：<EMAIL></li>
                        <li style="margin-bottom: 8px;">工作时间：9:00-18:00</li>
                    </ul>
                </div>
            </div>
            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #4a5568; color: #a0aec0; font-size: 12px;">
                Copyright © 2015 E-SHOP.COM All Rights Reserved
            </div>
        </div>
    </footer>

    <script src="js/common.js"></script>
    <script src="js/product-detail.js"></script>
</body>
</html>

