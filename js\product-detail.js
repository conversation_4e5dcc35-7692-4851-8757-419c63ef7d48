// 产品详情页JavaScript功能

// 切换主图
function changeMainImage(thumbnail) {
    const mainImage = document.getElementById('mainImage');
    const thumbnails = document.querySelectorAll('.thumbnail');
    
    // 更新主图
    mainImage.src = thumbnail.src;
    mainImage.alt = thumbnail.alt;
    
    // 更新缩略图状态
    thumbnails.forEach(thumb => thumb.classList.remove('active'));
    thumbnail.classList.add('active');
    
    // 添加切换动画
    mainImage.style.opacity = '0';
    setTimeout(() => {
        mainImage.style.transition = 'opacity 0.3s ease';
        mainImage.style.opacity = '1';
    }, 50);
}

// 增加数量
function increaseQuantity() {
    const input = document.getElementById('quantityInput');
    const currentValue = parseInt(input.value);
    const maxValue = parseInt(input.max);
    
    if (currentValue < maxValue) {
        input.value = currentValue + 1;
        updateSelectedQuantity();
    }
}

// 减少数量
function decreaseQuantity() {
    const input = document.getElementById('quantityInput');
    const currentValue = parseInt(input.value);
    const minValue = parseInt(input.min);
    
    if (currentValue > minValue) {
        input.value = currentValue - 1;
        updateSelectedQuantity();
    }
}

// 更新已选择数量显示
function updateSelectedQuantity() {
    const input = document.getElementById('quantityInput');
    const selectedQuantity = document.getElementById('selectedQuantity');
    selectedQuantity.textContent = input.value;
}

// 切换标签页
function switchTab(tabName) {
    // 移除所有标签头的活动状态
    document.querySelectorAll('.tab-header').forEach(header => {
        header.classList.remove('active');
    });
    
    // 隐藏所有标签内容
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    
    // 激活当前标签
    event.target.classList.add('active');
    document.getElementById(tabName).classList.add('active');
    
    // 添加切换动画
    const activeContent = document.getElementById(tabName);
    activeContent.style.opacity = '0';
    activeContent.style.transform = 'translateY(10px)';
    
    setTimeout(() => {
        activeContent.style.transition = 'all 0.3s ease';
        activeContent.style.opacity = '1';
        activeContent.style.transform = 'translateY(0)';
    }, 50);
}

// 图片放大功能
function initializeImageZoom() {
    const mainImage = document.getElementById('mainImage');
    
    mainImage.addEventListener('click', function() {
        openImageModal(this.src, this.alt);
    });
    
    // 鼠标悬停放大效果
    mainImage.addEventListener('mouseenter', function() {
        this.style.cursor = 'zoom-in';
        this.style.transform = 'scale(1.05)';
        this.style.transition = 'transform 0.3s ease';
    });
    
    mainImage.addEventListener('mouseleave', function() {
        this.style.transform = 'scale(1)';
    });
}

// 打开图片模态框
function openImageModal(src, alt) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'image-modal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        cursor: zoom-out;
    `;
    
    const img = document.createElement('img');
    img.src = src;
    img.alt = alt;
    img.style.cssText = `
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
        border-radius: 8px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    `;
    
    modal.appendChild(img);
    document.body.appendChild(modal);
    
    // 点击关闭
    modal.addEventListener('click', function() {
        document.body.removeChild(modal);
    });
    
    // ESC键关闭
    const handleKeyPress = function(e) {
        if (e.key === 'Escape') {
            document.body.removeChild(modal);
            document.removeEventListener('keydown', handleKeyPress);
        }
    };
    document.addEventListener('keydown', handleKeyPress);
}

// 产品规格展开/收起
function initializeSpecsToggle() {
    const specs = document.querySelector('.product-specs');
    const specItems = specs.querySelectorAll('.spec-item');
    
    if (specItems.length > 5) {
        // 隐藏多余的规格项
        for (let i = 5; i < specItems.length; i++) {
            specItems[i].style.display = 'none';
        }
        
        // 添加展开按钮
        const toggleBtn = document.createElement('button');
        toggleBtn.textContent = '查看更多规格 ▼';
        toggleBtn.className = 'specs-toggle-btn';
        toggleBtn.style.cssText = `
            background: none;
            border: none;
            color: #e53e3e;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
            transition: color 0.3s ease;
        `;
        
        let isExpanded = false;
        
        toggleBtn.addEventListener('click', function() {
            isExpanded = !isExpanded;
            
            for (let i = 5; i < specItems.length; i++) {
                specItems[i].style.display = isExpanded ? 'flex' : 'none';
            }
            
            this.textContent = isExpanded ? '收起规格 ▲' : '查看更多规格 ▼';
        });
        
        specs.appendChild(toggleBtn);
    }
}

// 相关产品推荐
function loadRelatedProducts() {
    // 模拟加载相关产品
    const relatedProducts = [
        {
            id: 301,
            name: '神舟AMD860K/4G/2G独显120G固态硬盘台式机DIY组装机',
            price: 1788.00,
            image: 'images/products/bxite-G2015051717070001.jpg'
        },
        {
            id: 302,
            name: '明基（BenQ）MS3081 投影仪会议投影机',
            price: 2199.00,
            image: 'images/products/bxite-G2015051801390001.jpg'
        }
    ];
    
    const container = document.querySelector('#recommend .products-grid');
    
    relatedProducts.forEach(product => {
        const productCard = createProductCard(product);
        container.appendChild(productCard);
    });
}

// 创建产品卡片
function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'product-card';
    card.innerHTML = `
        <div class="product-image">
            <img src="${product.image}" alt="${product.name}">
        </div>
        <div class="product-info">
            <h3 class="product-title">${product.name}</h3>
            <div class="product-price">
                <span class="current-price">¥${product.price.toFixed(2)}</span>
            </div>
            <div class="product-actions">
                <button class="add-to-cart" onclick="addToCart(${product.id})">加入购物车</button>
                <button class="add-to-wishlist" onclick="addToWishlist(${product.id})">♡</button>
            </div>
        </div>
    `;
    return card;
}

// 滚动到评价区域
function scrollToReviews() {
    switchTab('reviews');
    document.getElementById('reviews').scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
}

// 分享产品
function shareProduct() {
    const productTitle = document.querySelector('.product-detail-title').textContent;
    const productUrl = window.location.href;
    
    if (navigator.share) {
        navigator.share({
            title: productTitle,
            url: productUrl
        }).catch(console.error);
    } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(productUrl).then(() => {
            showNotification('产品链接已复制到剪贴板！', 'success');
        }).catch(() => {
            showNotification('分享功能暂不可用', 'error');
        });
    }
}

// 添加到比较列表
function addToCompare(productId) {
    let compareList = JSON.parse(localStorage.getItem('compareList')) || [];
    
    if (compareList.length >= 3) {
        showNotification('最多只能比较3个产品！', 'error');
        return;
    }
    
    if (!compareList.includes(productId)) {
        compareList.push(productId);
        localStorage.setItem('compareList', JSON.stringify(compareList));
        showNotification('已添加到比较列表！', 'success');
        updateCompareCount();
    } else {
        showNotification('该产品已在比较列表中！', 'info');
    }
}

// 更新比较数量显示
function updateCompareCount() {
    const compareList = JSON.parse(localStorage.getItem('compareList')) || [];
    const compareBtn = document.querySelector('.compare-btn');
    
    if (compareBtn) {
        compareBtn.textContent = `比较 (${compareList.length})`;
    }
}

// 初始化产品详情页功能
function initializeProductDetail() {
    // 数量输入框事件
    const quantityInput = document.getElementById('quantityInput');
    if (quantityInput) {
        quantityInput.addEventListener('change', updateSelectedQuantity);
        quantityInput.addEventListener('input', updateSelectedQuantity);
    }
    
    // 初始化图片放大
    initializeImageZoom();
    
    // 初始化规格展开
    initializeSpecsToggle();
    
    // 加载相关产品
    loadRelatedProducts();
    
    // 更新比较数量
    updateCompareCount();
    
    // 添加键盘导航支持
    document.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowLeft') {
            // 切换到上一张图片
            const thumbnails = document.querySelectorAll('.thumbnail');
            const activeThumbnail = document.querySelector('.thumbnail.active');
            const currentIndex = Array.from(thumbnails).indexOf(activeThumbnail);
            
            if (currentIndex > 0) {
                changeMainImage(thumbnails[currentIndex - 1]);
            }
        } else if (e.key === 'ArrowRight') {
            // 切换到下一张图片
            const thumbnails = document.querySelectorAll('.thumbnail');
            const activeThumbnail = document.querySelector('.thumbnail.active');
            const currentIndex = Array.from(thumbnails).indexOf(activeThumbnail);
            
            if (currentIndex < thumbnails.length - 1) {
                changeMainImage(thumbnails[currentIndex + 1]);
            }
        }
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeProductDetail();
    
    // 添加页面加载动画
    const elements = [
        '.product-gallery',
        '.product-details',
        '.product-tabs'
    ];
    
    elements.forEach((selector, index) => {
        const element = document.querySelector(selector);
        if (element) {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                element.style.transition = 'all 0.6s ease';
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, index * 200);
        }
    });
});

