# HTML网站转换为C#/.NET项目详细指南

## 概述

本指南将帮助您将现有的HTML电商网站转换为基于ASP.NET Core的C#项目。我们将逐步介绍如何将静态HTML页面转换为动态的Razor页面，并集成后端功能。

## 第一步：创建ASP.NET Core项目

### 1.1 使用Visual Studio创建项目
```bash
# 使用.NET CLI创建项目
dotnet new mvc -n EShopWebsite
cd EShopWebsite
```

### 1.2 项目结构
```
EShopWebsite/
├── Controllers/
│   ├── HomeController.cs
│   ├── ProductsController.cs
│   └── AccountController.cs
├── Models/
│   ├── Product.cs
│   ├── User.cs
│   └── ViewModels/
├── Views/
│   ├── Home/
│   ├── Products/
│   ├── Account/
│   └── Shared/
├── wwwroot/
│   ├── css/
│   ├── js/
│   └── images/
├── Data/
│   └── ApplicationDbContext.cs
└── Program.cs
```

## 第二步：数据模型设计

### 2.1 产品模型 (Models/Product.cs)
```csharp
using System.ComponentModel.DataAnnotations;

namespace EShopWebsite.Models
{
    public class Product
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(200)]
        public string Name { get; set; }
        
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; }
        
        public decimal? OriginalPrice { get; set; }
        
        [StringLength(500)]
        public string Description { get; set; }
        
        [StringLength(200)]
        public string ImageUrl { get; set; }
        
        public int CategoryId { get; set; }
        public Category Category { get; set; }
        
        public int Stock { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        
        // 导航属性
        public ICollection<CartItem> CartItems { get; set; }
        public ICollection<OrderItem> OrderItems { get; set; }
    }
}
```

### 2.2 分类模型 (Models/Category.cs)
```csharp
namespace EShopWebsite.Models
{
    public class Category
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; }
        
        [StringLength(300)]
        public string Description { get; set; }
        
        public int? ParentId { get; set; }
        public Category Parent { get; set; }
        public ICollection<Category> Children { get; set; }
        
        public ICollection<Product> Products { get; set; }
    }
}
```

### 2.3 用户模型 (Models/User.cs)
```csharp
using Microsoft.AspNetCore.Identity;

namespace EShopWebsite.Models
{
    public class ApplicationUser : IdentityUser
    {
        [StringLength(100)]
        public string FirstName { get; set; }
        
        [StringLength(100)]
        public string LastName { get; set; }
        
        public DateTime? DateOfBirth { get; set; }
        public DateTime CreatedAt { get; set; }
        
        // 导航属性
        public ICollection<Order> Orders { get; set; }
        public ICollection<CartItem> CartItems { get; set; }
    }
}
```

### 2.4 购物车模型 (Models/CartItem.cs)
```csharp
namespace EShopWebsite.Models
{
    public class CartItem
    {
        public int Id { get; set; }
        public string UserId { get; set; }
        public ApplicationUser User { get; set; }
        
        public int ProductId { get; set; }
        public Product Product { get; set; }
        
        public int Quantity { get; set; }
        public DateTime CreatedAt { get; set; }
    }
}
```

## 第三步：数据库上下文

### 3.1 ApplicationDbContext (Data/ApplicationDbContext.cs)
```csharp
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using EShopWebsite.Models;

namespace EShopWebsite.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }
        
        public DbSet<Product> Products { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<CartItem> CartItems { get; set; }
        public DbSet<Order> Orders { get; set; }
        public DbSet<OrderItem> OrderItems { get; set; }
        
        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);
            
            // 配置实体关系
            builder.Entity<Product>()
                .HasOne(p => p.Category)
                .WithMany(c => c.Products)
                .HasForeignKey(p => p.CategoryId);
                
            builder.Entity<CartItem>()
                .HasOne(c => c.User)
                .WithMany(u => u.CartItems)
                .HasForeignKey(c => c.UserId);
                
            builder.Entity<CartItem>()
                .HasOne(c => c.Product)
                .WithMany(p => p.CartItems)
                .HasForeignKey(c => c.ProductId);
                
            // 种子数据
            SeedData(builder);
        }
        
        private void SeedData(ModelBuilder builder)
        {
            // 添加默认分类
            builder.Entity<Category>().HasData(
                new Category { Id = 1, Name = "电脑配件", Description = "CPU、主板、显卡等" },
                new Category { Id = 2, Name = "手机配件", Description = "手机壳、充电器等" },
                new Category { Id = 3, Name = "数码影音", Description = "相机、音响等" }
            );
            
            // 添加示例产品
            builder.Entity<Product>().HasData(
                new Product 
                { 
                    Id = 1, 
                    Name = "神舟AMD860K/4G/2G独显120G固态硬盘台式机DIY组装机", 
                    Price = 1788.00m, 
                    CategoryId = 1,
                    ImageUrl = "/images/products/bxite-G2015051717070001.jpg",
                    Stock = 10,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                },
                new Product 
                { 
                    Id = 2, 
                    Name = "乐视（H60-L01）配置 蓝白色 移动4G手机", 
                    Price = 1699.00m, 
                    CategoryId = 2,
                    ImageUrl = "/images/products/bxite-G2015051800550001.jpg",
                    Stock = 15,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                }
            );
        }
    }
}
```

## 第四步：控制器开发

### 4.1 首页控制器 (Controllers/HomeController.cs)
```csharp
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using EShopWebsite.Data;
using EShopWebsite.Models.ViewModels;

namespace EShopWebsite.Controllers
{
    public class HomeController : Controller
    {
        private readonly ApplicationDbContext _context;
        
        public HomeController(ApplicationDbContext context)
        {
            _context = context;
        }
        
        public async Task<IActionResult> Index()
        {
            var viewModel = new HomeViewModel
            {
                FeaturedProducts = await _context.Products
                    .Where(p => p.IsActive)
                    .Include(p => p.Category)
                    .Take(8)
                    .ToListAsync(),
                    
                Categories = await _context.Categories
                    .Where(c => c.ParentId == null)
                    .ToListAsync()
            };
            
            return View(viewModel);
        }
    }
}
```

### 4.2 产品控制器 (Controllers/ProductsController.cs)
```csharp
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using EShopWebsite.Data;
using EShopWebsite.Models.ViewModels;

namespace EShopWebsite.Controllers
{
    public class ProductsController : Controller
    {
        private readonly ApplicationDbContext _context;
        
        public ProductsController(ApplicationDbContext context)
        {
            _context = context;
        }
        
        public async Task<IActionResult> Index(string search, int? categoryId, int page = 1, int pageSize = 12)
        {
            var query = _context.Products
                .Where(p => p.IsActive)
                .Include(p => p.Category);
                
            // 搜索过滤
            if (!string.IsNullOrEmpty(search))
            {
                query = query.Where(p => p.Name.Contains(search) || p.Description.Contains(search));
            }
            
            // 分类过滤
            if (categoryId.HasValue)
            {
                query = query.Where(p => p.CategoryId == categoryId.Value);
            }
            
            var totalItems = await query.CountAsync();
            var products = await query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
                
            var viewModel = new ProductListViewModel
            {
                Products = products,
                Categories = await _context.Categories.ToListAsync(),
                CurrentPage = page,
                TotalPages = (int)Math.Ceiling(totalItems / (double)pageSize),
                SearchTerm = search,
                SelectedCategoryId = categoryId
            };
            
            return View(viewModel);
        }
        
        public async Task<IActionResult> Details(int id)
        {
            var product = await _context.Products
                .Include(p => p.Category)
                .FirstOrDefaultAsync(p => p.Id == id && p.IsActive);
                
            if (product == null)
            {
                return NotFound();
            }
            
            var relatedProducts = await _context.Products
                .Where(p => p.CategoryId == product.CategoryId && p.Id != id && p.IsActive)
                .Take(4)
                .ToListAsync();
                
            var viewModel = new ProductDetailViewModel
            {
                Product = product,
                RelatedProducts = relatedProducts
            };
            
            return View(viewModel);
        }
    }
}
```

## 第五步：视图模型

### 5.1 首页视图模型 (Models/ViewModels/HomeViewModel.cs)
```csharp
namespace EShopWebsite.Models.ViewModels
{
    public class HomeViewModel
    {
        public IEnumerable<Product> FeaturedProducts { get; set; }
        public IEnumerable<Category> Categories { get; set; }
    }
}
```

### 5.2 产品列表视图模型 (Models/ViewModels/ProductListViewModel.cs)
```csharp
namespace EShopWebsite.Models.ViewModels
{
    public class ProductListViewModel
    {
        public IEnumerable<Product> Products { get; set; }
        public IEnumerable<Category> Categories { get; set; }
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public string SearchTerm { get; set; }
        public int? SelectedCategoryId { get; set; }
    }
}
```

## 第六步：Razor视图转换

### 6.1 布局页面 (Views/Shared/_Layout.cshtml)
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewData["Title"] - e商城</title>
    <link rel="stylesheet" href="~/css/common.css" asp-append-version="true">
    @await RenderSectionAsync("Styles", required: false)
</head>
<body>
    <!-- 头部 -->
    <header class="header">
        <!-- 顶部栏 -->
        <div class="top-bar">
            <div class="container">
                <div class="top-links">
                    <a href="#" onclick="addToFavorites()">⭐ 收藏本站</a>
                    @if (User.Identity.IsAuthenticated)
                    {
                        <a asp-controller="Account" asp-action="Profile">@User.Identity.Name</a>
                        <a asp-controller="Account" asp-action="Logout">退出</a>
                    }
                    else
                    {
                        <a asp-controller="Account" asp-action="Login">登录</a>
                        <a asp-controller="Account" asp-action="Register">注册</a>
                    }
                    <a href="#">手机版</a>
                    <a href="#">客户服务</a>
                </div>
                <div class="cart-info">
                    <a asp-controller="Cart" asp-action="Index" style="color: inherit; text-decoration: none;">
                        🛒 我的购物车 <span class="cart-count" id="cartCount">0</span>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 主头部 -->
        <div class="main-header">
            <div class="container">
                <a asp-controller="Home" asp-action="Index" class="logo">
                    商城 <span style="font-size: 14px; color: #666;">多·好·快·省</span>
                </a>
                
                <form asp-controller="Products" asp-action="Index" method="get" class="search-box">
                    <input type="text" name="search" class="search-input" 
                           placeholder="热门搜索：小米手机、固态硬盘、单反相机、机械键盘、空气净化器"
                           value="@ViewBag.SearchTerm">
                    <button type="submit" class="search-btn">搜索</button>
                </form>
            </div>
        </div>
        
        <!-- 导航菜单 -->
        <nav class="nav-menu">
            <div class="container">
                <button class="category-dropdown" onclick="toggleCategoryMenu()">
                    全部商品分类 ▼
                </button>
                <ul class="main-nav">
                    <li><a asp-controller="Home" asp-action="Index">首页</a></li>
                    <li><a asp-controller="Products" asp-action="Index" asp-route-categoryId="1">电脑配件</a></li>
                    <li><a asp-controller="Products" asp-action="Index" asp-route-categoryId="2">手机配件</a></li>
                    <li><a href="#">团购</a></li>
                    <li><a href="#">闪购</a></li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- 主要内容 -->
    <main>
        @RenderBody()
    </main>

    <!-- 页脚 -->
    <footer style="background: #2d3748; color: white; padding: 40px 0; margin-top: 60px;">
        <div class="container">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 30px;">
                <div>
                    <h4 style="margin-bottom: 15px;">关于我们</h4>
                    <p style="color: #a0aec0; font-size: 14px; line-height: 1.6;">
                        e商城致力于为用户提供优质的购物体验，多·好·快·省是我们的服务理念。
                    </p>
                </div>
                <div>
                    <h4 style="margin-bottom: 15px;">客户服务</h4>
                    <ul style="list-style: none; color: #a0aec0; font-size: 14px;">
                        <li style="margin-bottom: 8px;"><a href="#" style="color: inherit; text-decoration: none;">帮助中心</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: inherit; text-decoration: none;">售后服务</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: inherit; text-decoration: none;">配送说明</a></li>
                    </ul>
                </div>
                <div>
                    <h4 style="margin-bottom: 15px;">联系我们</h4>
                    <ul style="list-style: none; color: #a0aec0; font-size: 14px;">
                        <li style="margin-bottom: 8px;">客服热线：400-123-4567</li>
                        <li style="margin-bottom: 8px;">邮箱：<EMAIL></li>
                        <li style="margin-bottom: 8px;">工作时间：9:00-18:00</li>
                    </ul>
                </div>
            </div>
            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #4a5568; color: #a0aec0; font-size: 12px;">
                Copyright © 2015 E-SHOP.COM All Rights Reserved
            </div>
        </div>
    </footer>

    <script src="~/js/common.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
```

### 6.2 首页视图 (Views/Home/Index.cshtml)
```html
@model HomeViewModel
@{
    ViewData["Title"] = "首页";
}

@section Styles {
    <link rel="stylesheet" href="~/css/index.css" asp-append-version="true">
}

<!-- 主要内容区域 -->
<div class="container">
    <div class="main-content">
        <!-- 左侧分类菜单 -->
        <aside class="sidebar">
            <div class="category-menu">
                <h3 class="category-title">所有分类</h3>
                <ul class="category-list">
                    @foreach (var category in Model.Categories)
                    {
                        <li class="category-item">
                            <a asp-controller="Products" asp-action="Index" asp-route-categoryId="@category.Id">
                                @category.Name
                            </a>
                        </li>
                    }
                </ul>
            </div>
        </aside>

        <!-- 右侧内容区域 -->
        <div class="content-area">
            <!-- 轮播图 -->
            <div class="carousel" id="mainCarousel">
                <div class="carousel-slides">
                    <div class="carousel-slide active">
                        <img src="~/images/banners/banner1.jpg" alt="轮播图1">
                    </div>
                    <div class="carousel-slide">
                        <img src="~/images/banners/banner2.jpg" alt="轮播图2">
                    </div>
                </div>
                
                <div class="carousel-indicators">
                    <button class="carousel-indicator active" onclick="goToSlide(0)"></button>
                    <button class="carousel-indicator" onclick="goToSlide(1)"></button>
                </div>
                
                <button class="carousel-nav carousel-prev" onclick="prevSlide()">‹</button>
                <button class="carousel-nav carousel-next" onclick="nextSlide()">›</button>
            </div>
        </div>
    </div>

    <!-- 热品推荐 -->
    <section class="featured-products">
        <h2 class="section-title">热品推荐</h2>
        <div class="products-grid">
            @foreach (var product in Model.FeaturedProducts)
            {
                <div class="product-card">
                    <div class="product-image">
                        <a asp-controller="Products" asp-action="Details" asp-route-id="@product.Id">
                            <img src="@product.ImageUrl" alt="@product.Name">
                        </a>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">
                            <a asp-controller="Products" asp-action="Details" asp-route-id="@product.Id">
                                @product.Name
                            </a>
                        </h3>
                        <div class="product-price">
                            <span class="current-price">¥@product.Price.ToString("F2")</span>
                            @if (product.OriginalPrice.HasValue && product.OriginalPrice > product.Price)
                            {
                                <span class="original-price">¥@product.OriginalPrice.Value.ToString("F2")</span>
                            }
                        </div>
                        <div class="product-actions">
                            <button class="add-to-cart" onclick="addToCart(@product.Id)">加入购物车</button>
                            <button class="add-to-wishlist" onclick="addToWishlist(@product.Id)">♡</button>
                        </div>
                    </div>
                </div>
            }
        </div>
    </section>
</div>

@section Scripts {
    <script src="~/js/carousel.js" asp-append-version="true"></script>
}
```

## 第七步：配置和启动

### 7.1 Program.cs配置
```csharp
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using EShopWebsite.Data;
using EShopWebsite.Models;

var builder = WebApplication.CreateBuilder(args);

// 添加服务
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

builder.Services.AddDefaultIdentity<ApplicationUser>(options => 
{
    options.SignIn.RequireConfirmedAccount = false;
    options.Password.RequireDigit = true;
    options.Password.RequiredLength = 6;
})
.AddEntityFrameworkStores<ApplicationDbContext>();

builder.Services.AddControllersWithViews();

var app = builder.Build();

// 配置HTTP请求管道
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.MapRazorPages();

app.Run();
```

### 7.2 appsettings.json配置
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=EShopWebsite;Trusted_Connection=true;MultipleActiveResultSets=true"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*"
}
```

## 第八步：数据库迁移

```bash
# 添加初始迁移
dotnet ef migrations add InitialCreate

# 更新数据库
dotnet ef database update
```

## 第九步：API接口开发

### 9.1 购物车API (Controllers/Api/CartController.cs)
```csharp
[ApiController]
[Route("api/[controller]")]
public class CartController : ControllerBase
{
    private readonly ApplicationDbContext _context;
    private readonly UserManager<ApplicationUser> _userManager;
    
    public CartController(ApplicationDbContext context, UserManager<ApplicationUser> userManager)
    {
        _context = context;
        _userManager = userManager;
    }
    
    [HttpPost("add")]
    [Authorize]
    public async Task<IActionResult> AddToCart([FromBody] AddToCartRequest request)
    {
        var userId = _userManager.GetUserId(User);
        
        var existingItem = await _context.CartItems
            .FirstOrDefaultAsync(c => c.UserId == userId && c.ProductId == request.ProductId);
            
        if (existingItem != null)
        {
            existingItem.Quantity += request.Quantity;
        }
        else
        {
            var cartItem = new CartItem
            {
                UserId = userId,
                ProductId = request.ProductId,
                Quantity = request.Quantity,
                CreatedAt = DateTime.Now
            };
            _context.CartItems.Add(cartItem);
        }
        
        await _context.SaveChangesAsync();
        
        var cartCount = await _context.CartItems
            .Where(c => c.UserId == userId)
            .SumAsync(c => c.Quantity);
            
        return Ok(new { success = true, cartCount });
    }
    
    [HttpGet("count")]
    [Authorize]
    public async Task<IActionResult> GetCartCount()
    {
        var userId = _userManager.GetUserId(User);
        var count = await _context.CartItems
            .Where(c => c.UserId == userId)
            .SumAsync(c => c.Quantity);
            
        return Ok(new { count });
    }
}

public class AddToCartRequest
{
    public int ProductId { get; set; }
    public int Quantity { get; set; } = 1;
}
```

## 第十步：前端JavaScript更新

### 10.1 更新common.js中的购物车功能
```javascript
// 添加到购物车 - 更新为调用API
async function addToCart(productId, quantity = 1) {
    try {
        const response = await fetch('/api/cart/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
            },
            body: JSON.stringify({
                productId: productId,
                quantity: quantity
            })
        });
        
        if (response.ok) {
            const result = await response.json();
            updateCartCount(result.cartCount);
            showNotification('商品已添加到购物车！', 'success');
        } else if (response.status === 401) {
            showNotification('请先登录！', 'error');
            window.location.href = '/Account/Login';
        } else {
            showNotification('添加失败，请重试！', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('网络错误，请重试！', 'error');
    }
}

// 更新购物车数量显示
function updateCartCount(count) {
    const cartCountElement = document.getElementById('cartCount');
    if (cartCountElement) {
        cartCountElement.textContent = count || 0;
    }
}

// 页面加载时获取购物车数量
document.addEventListener('DOMContentLoaded', async function() {
    try {
        const response = await fetch('/api/cart/count');
        if (response.ok) {
            const result = await response.json();
            updateCartCount(result.count);
        }
    } catch (error) {
        console.error('Error loading cart count:', error);
    }
});
```

## 总结

通过以上步骤，您已经成功将HTML网站转换为功能完整的ASP.NET Core应用程序。主要改进包括：

1. **数据驱动**：从静态HTML转换为动态数据驱动的页面
2. **用户认证**：集成ASP.NET Core Identity进行用户管理
3. **数据库集成**：使用Entity Framework Core进行数据访问
4. **API接口**：提供RESTful API支持前端交互
5. **安全性**：添加了CSRF保护、用户认证等安全措施

接下来您可以继续添加：
- 订单管理功能
- 支付集成
- 管理后台
- 邮件通知
- 缓存优化
- 日志记录

这个转换指南为您提供了一个完整的基础框架，您可以根据具体需求进行扩展和定制。

