// 通用JavaScript功能

// 购物车数据
let cart = JSON.parse(localStorage.getItem('cart')) || [];
let wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];

// 更新购物车数量显示
function updateCartCount() {
    const cartCount = document.querySelector('.cart-count');
    if (cartCount) {
        cartCount.textContent = cart.length;
    }
}

// 添加到购物车
function addToCart(productId) {
    // 模拟产品数据
    const products = {
        1: { id: 1, name: '神舟AMD860K/4G/2G独显120G固态硬盘台式机DIY组装机', price: 1788.00, image: 'images/products/bxite-G2015051717070001.jpg' },
        2: { id: 2, name: '乐视（H60-L01）配置 蓝白色 移动4G手机', price: 1699.00, image: 'images/products/bxite-G2015051800550001.jpg' },
        3: { id: 3, name: '尼康（Nikon）D3200 单反相机套机（AF-S DX）', price: 3309.00, image: 'images/products/bxite-G2015051801210001.jpg' },
        4: { id: 4, name: '酷青(cocoa)K5U 50 蓝牙智能运动手环蓝牙', price: 2799.00, image: 'images/products/bxite-G2015051801300001.jpg' },
        5: { id: 5, name: '明基（BenQ）MS3081 投影仪会议投影机', price: 2199.00, image: 'images/products/bxite-G2015051801390001.jpg' },
        6: { id: 6, name: 'TP-LINK TL-WR886N 4 50M无线路由器（写意白）', price: 99.00, image: 'images/products/bxite-G2015051801470001.jpg' },
        7: { id: 7, name: '神舟AMD860K/4G/2G独显120G固态硬盘台式机DIY组装机', price: 1788.00, image: 'images/products/bxite-G2015051717070001.jpg' },
        8: { id: 8, name: '欣睿宇(XXY) 酷睿i3 4160/4G/1TB/独显/台式机', price: 1659.00, image: 'images/products/bxite-G2015051801580001.jpg' }
    };

    const product = products[productId];
    if (product) {
        // 检查是否已在购物车中
        const existingItem = cart.find(item => item.id === productId);
        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            cart.push({ ...product, quantity: 1 });
        }
        
        localStorage.setItem('cart', JSON.stringify(cart));
        updateCartCount();
        showNotification('商品已添加到购物车！', 'success');
    }
}

// 添加到收藏夹
function addToWishlist(productId) {
    if (!wishlist.includes(productId)) {
        wishlist.push(productId);
        localStorage.setItem('wishlist', JSON.stringify(wishlist));
        showNotification('商品已添加到收藏夹！', 'success');
    } else {
        showNotification('商品已在收藏夹中！', 'info');
    }
}

// 收藏网站
function addToFavorites() {
    try {
        if (window.external && window.external.addFavorite) {
            // IE
            window.external.addFavorite(window.location.href, document.title);
        } else if (window.sidebar && window.sidebar.addPanel) {
            // Firefox
            window.sidebar.addPanel(document.title, window.location.href, '');
        } else {
            // 其他浏览器
            showNotification('请使用 Ctrl+D 收藏本站！', 'info');
        }
    } catch (e) {
        showNotification('请使用 Ctrl+D 收藏本站！', 'info');
    }
}

// 执行搜索
function performSearch() {
    const searchInput = document.querySelector('.search-input');
    const query = searchInput.value.trim();
    if (query) {
        window.location.href = `products.html?search=${encodeURIComponent(query)}`;
    }
}

// 切换分类菜单
function toggleCategoryMenu() {
    // 这里可以添加下拉菜单的显示/隐藏逻辑
    showNotification('分类菜单功能开发中...', 'info');
}

// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // 添加样式
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#48bb78' : type === 'error' ? '#f56565' : '#4299e1'};
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        font-size: 14px;
        font-weight: 500;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// 格式化价格
function formatPrice(price) {
    return '¥' + price.toFixed(2);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    updateCartCount();
    
    // 搜索框回车事件
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }
    
    // 添加页面加载动画
    document.body.style.opacity = '0';
    setTimeout(() => {
        document.body.style.transition = 'opacity 0.5s ease';
        document.body.style.opacity = '1';
    }, 100);
});

// 平滑滚动到顶部
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// 检查元素是否在视口中
function isInViewport(element) {
    const rect = element.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}

// 懒加载图片
function lazyLoadImages() {
    const images = document.querySelectorAll('img[data-src]');
    images.forEach(img => {
        if (isInViewport(img)) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
        }
    });
}

// 监听滚动事件进行懒加载
window.addEventListener('scroll', lazyLoadImages);

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

